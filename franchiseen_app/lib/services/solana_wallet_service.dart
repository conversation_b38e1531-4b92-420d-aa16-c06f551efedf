import 'dart:convert';
import 'dart:typed_data';
import 'package:solana/solana.dart';
import 'package:bip39/bip39.dart' as bip39;
import 'package:ed25519_hd_key/ed25519_hd_key.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../utils/error_handler.dart';

class SolanaWalletService {
  static const String _mnemonicKey = 'solana_mnemonic';
  static const String _privateKeyKey = 'solana_private_key';
  static const String _publicKeyKey = 'solana_public_key';

  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  late final SolanaClient _client;
  Ed25519HDKeyPair? _keyPair;

  // Solana devnet RPC endpoint - change to mainnet for production
  static const String _rpcUrl = 'https://api.devnet.solana.com';

  SolanaWalletService() {
    _client = SolanaClient(
      rpcUrl: Uri.parse(_rpcUrl),
      websocketUrl: Uri.parse('wss://api.devnet.solana.com'),
    );
  }

  /// Generate a new wallet with mnemonic phrase
  Future<String> generateWallet() async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      // Generate mnemonic
      final mnemonic = bip39.generateMnemonic();

      // Derive key pair from mnemonic
      final seed = bip39.mnemonicToSeed(mnemonic);
      final keyPair = await Ed25519HDKeyPair.fromMnemonic(mnemonic);

      // Store securely
      await _secureStorage.write(key: _mnemonicKey, value: mnemonic);
      final keyPairData = await keyPair.extract();
      await _secureStorage.write(
        key: _privateKeyKey,
        value: base64Encode(keyPairData.bytes),
      );
      await _secureStorage.write(key: _publicKeyKey, value: keyPair.address);

      _keyPair = keyPair;

      return mnemonic;
    }, 'Failed to generate wallet');
  }

  /// Import wallet from mnemonic phrase
  Future<void> importWallet(String mnemonic) async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      if (!bip39.validateMnemonic(mnemonic)) {
        throw Exception('Invalid mnemonic phrase');
      }

      // Derive key pair from mnemonic
      final keyPair = await Ed25519HDKeyPair.fromMnemonic(mnemonic);

      // Store securely
      await _secureStorage.write(key: _mnemonicKey, value: mnemonic);
      final keyPairData = await keyPair.extract();
      await _secureStorage.write(
        key: _privateKeyKey,
        value: base64Encode(keyPairData.bytes),
      );
      await _secureStorage.write(key: _publicKeyKey, value: keyPair.address);

      _keyPair = keyPair;
    }, 'Failed to import wallet');
  }

  /// Load existing wallet from secure storage
  Future<bool> loadWallet() async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      final mnemonic = await _secureStorage.read(key: _mnemonicKey);
      if (mnemonic == null) return false;

      _keyPair = await Ed25519HDKeyPair.fromMnemonic(mnemonic);
      return true;
    }, 'Failed to load wallet');
  }

  /// Get wallet address
  String? get address => _keyPair?.address;

  /// Check if wallet is loaded
  bool get isWalletLoaded => _keyPair != null;

  /// Get SOL balance
  Future<double> getBalance() async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      if (_keyPair == null) throw Exception('Wallet not loaded');

      final balance = await _client.rpcClient.getBalance(_keyPair!.address);
      return balance.value / lamportsPerSol;
    }, 'Failed to get balance');
  }

  /// Send SOL to another address
  Future<String> sendSOL({
    required String toAddress,
    required double amount,
  }) async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      if (_keyPair == null) throw Exception('Wallet not loaded');

      final lamports = (amount * lamportsPerSol).round();

      // Get recent blockhash
      final recentBlockhash = await _client.rpcClient.getRecentBlockhash();

      // Create transfer instruction
      final instruction = SystemInstruction.transfer(
        fundingAccount: _keyPair!.publicKey,
        recipientAccount: Ed25519HDPublicKey.fromBase58(toAddress),
        lamports: lamports,
      );

      // Create and sign transaction
      final transaction = Transaction(
        instructions: [instruction],
        recentBlockhash: recentBlockhash.value.blockhash,
        feePayer: _keyPair!.publicKey,
      );

      transaction.sign([_keyPair!]);

      // Send transaction
      final signature = await _client.rpcClient.sendAndConfirmTransaction(
        transaction.encode(),
      );

      return signature;
    }, 'Failed to send SOL');
  }

  /// Get transaction history
  Future<List<Map<String, dynamic>>> getTransactionHistory({
    int limit = 10,
  }) async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      if (_keyPair == null) throw Exception('Wallet not loaded');

      final signatures = await _client.rpcClient.getSignaturesForAddress(
        _keyPair!.address,
        limit: limit,
      );

      List<Map<String, dynamic>> transactions = [];

      for (final sig in signatures) {
        try {
          final tx = await _client.rpcClient.getTransaction(sig.signature);
          if (tx != null) {
            transactions.add({
              'signature': sig.signature,
              'slot': sig.slot,
              'blockTime': sig.blockTime,
              'confirmationStatus': sig.confirmationStatus,
              'err': sig.err,
              'memo': sig.memo,
            });
          }
        } catch (e) {
          // Skip failed transaction fetches
          continue;
        }
      }

      return transactions;
    }, 'Failed to get transaction history');
  }

  /// Clear wallet data (logout)
  Future<void> clearWallet() async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      await _secureStorage.delete(key: _mnemonicKey);
      await _secureStorage.delete(key: _privateKeyKey);
      await _secureStorage.delete(key: _publicKeyKey);
      _keyPair = null;
    }, 'Failed to clear wallet');
  }

  /// Get mnemonic phrase (for backup)
  Future<String?> getMnemonic() async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      return await _secureStorage.read(key: _mnemonicKey);
    }, 'Failed to get mnemonic');
  }

  /// Request airdrop (devnet only)
  Future<String> requestAirdrop({double amount = 1.0}) async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      if (_keyPair == null) throw Exception('Wallet not loaded');

      final lamports = (amount * lamportsPerSol).round();
      final signature = await _client.rpcClient.requestAirdrop(
        _keyPair!.address,
        lamports,
      );

      return signature;
    }, 'Failed to request airdrop');
  }
}
