import 'dart:convert';
import 'package:solana/solana.dart';
import 'solana_wallet_service.dart';
import '../utils/error_handler.dart';
import '../models/franchise.dart';

class SolanaPaymentService {
  final SolanaWalletService _walletService;
  late final SolanaClient _client;

  // Platform treasury wallet address (replace with actual address)
  static const String _treasuryAddress =
      'FranchiseenTreasuryWalletAddressHere123456789';

  // Platform fee percentage (e.g., 2.5%)
  static const double _platformFeePercentage = 0.025;

  SolanaPaymentService(this._walletService) {
    _client = SolanaClient(
      rpcUrl: Uri.parse('https://api.devnet.solana.com'),
      websocketUrl: Uri.parse('wss://api.devnet.solana.com'),
    );
  }

  /// Purchase franchise shares
  Future<String> purchaseFranchiseShares({
    required Franchise franchise,
    required int numberOfShares,
    required double pricePerShare,
  }) async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      if (!_walletService.isWalletLoaded) {
        throw Exception('Wallet not connected');
      }

      final totalAmount = numberOfShares * pricePerShare;
      final platformFee = totalAmount * _platformFeePercentage;
      final franchiseOwnerAmount = totalAmount - platformFee;

      // Check balance
      final balance = await _walletService.getBalance();
      if (balance < totalAmount) {
        throw Exception(
          'Insufficient balance. Required: $totalAmount SOL, Available: $balance SOL',
        );
      }

      // Create payment transactions
      final transactions = <String>[];

      // 1. Send platform fee to treasury
      if (platformFee > 0) {
        final feeSignature = await _walletService.sendSOL(
          toAddress: _treasuryAddress,
          amount: platformFee,
        );
        transactions.add(feeSignature);
      }

      // 2. Send payment to franchise owner
      final paymentSignature = await _walletService.sendSOL(
        toAddress: franchise.ownerId, // Assuming ownerId is the wallet address
        amount: franchiseOwnerAmount,
      );
      transactions.add(paymentSignature);

      // 3. Record the purchase (this would typically interact with smart contract)
      await _recordFranchisePurchase(
        franchise: franchise,
        buyerAddress: _walletService.address!,
        numberOfShares: numberOfShares,
        totalAmount: totalAmount,
        transactionSignatures: transactions,
      );

      return paymentSignature; // Return main payment signature
    }, 'Failed to purchase franchise shares');
  }

  /// Process monthly revenue distribution
  Future<List<String>> distributeRevenue({
    required String franchiseId,
    required double totalRevenue,
    required Map<String, double>
    shareholderDistribution, // address -> percentage
  }) async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      if (!_walletService.isWalletLoaded) {
        throw Exception('Wallet not connected');
      }

      final signatures = <String>[];

      // Distribute revenue to each shareholder
      for (final entry in shareholderDistribution.entries) {
        final shareholderAddress = entry.key;
        final percentage = entry.value;
        final amount = totalRevenue * percentage;

        if (amount > 0.001) {
          // Only send if amount is significant
          final signature = await _walletService.sendSOL(
            toAddress: shareholderAddress,
            amount: amount,
          );
          signatures.add(signature);
        }
      }

      return signatures;
    }, 'Failed to distribute revenue');
  }

  /// Create a payment request (for QR codes, etc.)
  Map<String, dynamic> createPaymentRequest({
    required String recipientAddress,
    required double amount,
    String? reference,
    String? label,
    String? message,
  }) {
    final params = <String, String>{
      'recipient': recipientAddress,
      'amount': amount.toString(),
    };

    if (reference != null) params['reference'] = reference;
    if (label != null) params['label'] = label;
    if (message != null) params['message'] = message;

    final queryString = params.entries
        .map(
          (e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}',
        )
        .join('&');

    return {
      'url': 'solana:$recipientAddress?$queryString',
      'recipient': recipientAddress,
      'amount': amount,
      'reference': reference,
      'label': label,
      'message': message,
    };
  }

  /// Validate payment transaction
  Future<bool> validatePayment({
    required String signature,
    required String expectedRecipient,
    required double expectedAmount,
  }) async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      final transaction = await _client.rpcClient.getTransaction(signature);

      if (transaction == null) return false;

      // Parse transaction to validate recipient and amount
      // This is a simplified validation - in production, you'd want more thorough checks

      return true; // Placeholder - implement actual validation logic
    }, 'Failed to validate payment');
  }

  /// Get payment history for user
  Future<List<Map<String, dynamic>>> getPaymentHistory() async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      if (!_walletService.isWalletLoaded) {
        throw Exception('Wallet not connected');
      }

      final transactions = await _walletService.getTransactionHistory(
        limit: 50,
      );

      // Filter and format payment-related transactions
      final paymentHistory = <Map<String, dynamic>>[];

      for (final tx in transactions) {
        // Parse transaction details and categorize
        paymentHistory.add({
          'signature': tx['signature'],
          'type': _determineTransactionType(tx),
          'amount': _extractTransactionAmount(tx),
          'timestamp': tx['blockTime'],
          'status': tx['err'] == null ? 'confirmed' : 'failed',
        });
      }

      return paymentHistory;
    }, 'Failed to get payment history');
  }

  /// Estimate transaction fee
  Future<double> estimateTransactionFee() async {
    return await ErrorHandler.executeWithErrorHandling(() async {
      // Get recent blockhash to estimate fee

      // Base fee for a simple transfer (in lamports)
      const baseFee = 5000; // 0.000005 SOL

      return baseFee / lamportsPerSol;
    }, 'Failed to estimate transaction fee');
  }

  /// Record franchise purchase (placeholder for smart contract interaction)
  Future<void> _recordFranchisePurchase({
    required Franchise franchise,
    required String buyerAddress,
    required int numberOfShares,
    required double totalAmount,
    required List<String> transactionSignatures,
  }) async {
    // This would typically interact with a smart contract to record the purchase
    // For now, we'll just log it or store it in a database

    final purchaseRecord = {
      'franchiseId': franchise.id,
      'buyerAddress': buyerAddress,
      'numberOfShares': numberOfShares,
      'totalAmount': totalAmount,
      'pricePerShare': totalAmount / numberOfShares,
      'transactionSignatures': transactionSignatures,
      'timestamp': DateTime.now().toIso8601String(),
    };

    // TODO: Store this record in your database or smart contract
    print('Franchise purchase recorded: ${jsonEncode(purchaseRecord)}');
  }

  /// Determine transaction type from transaction data
  String _determineTransactionType(Map<String, dynamic> transaction) {
    // Analyze transaction to determine if it's a purchase, revenue, etc.
    // This is a placeholder implementation
    return 'transfer';
  }

  /// Extract transaction amount from transaction data
  double _extractTransactionAmount(Map<String, dynamic> transaction) {
    // Parse transaction to extract the amount
    // This is a placeholder implementation
    return 0.0;
  }
}
