{"rustc": 17575471286409424799, "features": "[\"hash\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"cargo_toml\", \"event-cpi\", \"hash\", \"idl-build\", \"init-if-needed\", \"interface-instructions\"]", "target": 12443130540095201479, "profile": 14939500643692185782, "path": 17942639171429834693, "deps": [[2713742371683562785, "syn", false, 12837229425092551342], [3060637413840920116, "proc_macro2", false, 11099601508422595987], [6616501577376279788, "bs58", false, 18395719086939598107], [8008191657135824715, "thiserror", false, 14523505311689364213], [9689903380558560274, "serde", false, 14605429907638539088], [9857275760291862238, "sha2", false, 10967429944496277324], [13625485746686963219, "anyhow", false, 17511838721780555517], [16131248048418321657, "heck", false, 2635702984170245253], [16362055519698394275, "serde_json", false, 15205605353591103153], [17990358020177143287, "quote", false, 870420590172173004]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-syn-661526eba042b0c0/dep-lib-anchor_syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}