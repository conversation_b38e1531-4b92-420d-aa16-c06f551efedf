{"rustc": 17575471286409424799, "features": "[\"confidential-hook\", \"default\", \"no-entrypoint\", \"token-group\", \"zk-ops\"]", "declared_features": "[\"confidential-hook\", \"default\", \"no-entrypoint\", \"serde-traits\", \"test-sbf\", \"token-group\", \"zk-ops\"]", "target": 10419295424445391726, "profile": 8276155916380437441, "path": 8580801119576901284, "deps": [[3341616989126782036, "spl_token_metadata_interface", false, 1711116879243002582], [3586904800756451376, "spl_token", false, 9000400538625589485], [5157631553186200874, "num_traits", false, 13009177868235947546], [5274774017022604959, "spl_memo", false, 9995958100431404497], [6643739152182419278, "bytemuck", false, 13465585674597037252], [8008191657135824715, "thiserror", false, 4538958720104521460], [9529943735784919782, "arrayref", false, 16217418545956212235], [11128316282551294881, "spl_pod", false, 11853271019816047248], [11263754829263059703, "num_derive", false, 9229471641279669281], [13789900403929416848, "solana_zk_token_sdk", false, 12420507101219262057], [15166169964310602942, "spl_transfer_hook_interface", false, 15924190752212359109], [15222034437690075872, "spl_type_length_value", false, 8092816564374268059], [15553188696078906047, "spl_token_group_interface", false, 9477669053505272308], [16712258961403650142, "num_enum", false, 1782710617605517186], [17231035582851916918, "solana_program", false, 7143540289734359807], [17909568817133603617, "solana_security_txt", false, 3454550797545101605]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-token-2022-0f85d84e9bce8814/dep-lib-spl_token_2022", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}