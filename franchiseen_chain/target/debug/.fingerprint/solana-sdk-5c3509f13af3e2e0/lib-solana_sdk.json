{"rustc": 17575471286409424799, "features": "[\"borsh\", \"byteorder\", \"chrono\", \"default\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\"]", "declared_features": "[\"borsh\", \"byteorder\", \"chrono\", \"curve25519-dalek\", \"default\", \"dev-context-only-utils\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"frozen-abi\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"program\", \"qualifier_attr\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\"]", "target": 18327772260806902435, "profile": 8276155916380437441, "path": 16156238600123197074, "deps": [[65234016722529558, "bincode", false, 8378392133955702329], [757899038044743028, "serde_with", false, 397125162681030297], [1230783206204459120, "urip<PERSON>e", false, 13274512251722475154], [3712811570531045576, "byteorder", false, 4755074895975334729], [4258399515347749257, "pbkdf2", false, 1024192298651315929], [4731167174326621189, "rand0_7", false, 8674787314126584615], [5092398082731730447, "derivation_path", false, 15028833515641487536], [5986029879202738730, "log", false, 3769924884449324755], [6157328561513292295, "build_script_build", false, 9693297399805348652], [6203123018298125816, "borsh", false, 10422699441341185116], [6616501577376279788, "bs58", false, 8062723790362687506], [6643739152182419278, "bytemuck", false, 13465585674597037252], [7896293946984509699, "bitflags", false, 11543194035733796943], [8008191657135824715, "thiserror", false, 4538958720104521460], [8079500665534101559, "siphasher", false, 10288614187972228799], [8392100871557128840, "solana_sdk_macro", false, 18101255302909137256], [9209347893430674936, "hmac", false, 1712831853924795496], [9547749918651864678, "bytemuck_derive", false, 13870451313912880782], [9689903380558560274, "serde", false, 14160843880739812629], [9857275760291862238, "sha2", false, 1484776209726563482], [9897246384292347999, "chrono", false, 13935687490523112496], [10504454274054532777, "memmap2", false, 528621231621398391], [10520923840501062997, "generic_array", false, 3193457674487344455], [10697153736615144157, "libsecp256k1", false, 17490039938172271052], [10889494155287625682, "serde_bytes", false, 9631362910416424855], [11017232866922121725, "sha3", false, 16441812699487704154], [13024038960712206194, "qstring", false, 17040891641070496907], [13208667028893622512, "rand", false, 494806898738333222], [14156967978702956262, "rustversion", false, 6004720243463137720], [14931062873021150766, "itertools", false, 3527054262153667654], [15407337108592562583, "ed25519_dalek_bip32", false, 5805465786269886991], [16257276029081467297, "serde_derive", false, 5775347667538681514], [16362055519698394275, "serde_json", false, 13521650517557272512], [16712258961403650142, "num_enum", false, 1782710617605517186], [17231035582851916918, "solana_program", false, 7143540289734359807], [17475753849556516473, "digest", false, 15573557816089349480], [17917672826516349275, "lazy_static", false, 6247634492949568233], [17987314850127689447, "ed25519_dalek", false, 339989027378393763]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-sdk-5c3509f13af3e2e0/dep-lib-solana_sdk", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}