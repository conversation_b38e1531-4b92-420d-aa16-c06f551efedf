{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 155187846257117564, "path": 4790136378252857919, "deps": [[3060637413840920116, "proc_macro2", false, 11099601508422595987], [4974441333307933176, "syn", false, 7667610102674393800], [14299170049494554845, "wasm_bindgen_shared", false, 13220822778172157469], [14372503175394433084, "wasm_bindgen_backend", false, 17619579368735755453], [17990358020177143287, "quote", false, 870420590172173004]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-macro-support-b726fdceeb006a1f/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}