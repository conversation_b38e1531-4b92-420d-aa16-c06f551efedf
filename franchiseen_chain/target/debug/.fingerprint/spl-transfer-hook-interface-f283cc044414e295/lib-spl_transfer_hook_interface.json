{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[]", "target": 775285158232319892, "profile": 8276155916380437441, "path": 4362592607876633224, "deps": [[927191479211496183, "spl_tlv_account_resolution", false, 11556375340106957156], [2330260416847750317, "spl_discriminator", false, 1323373359992733819], [6643739152182419278, "bytemuck", false, 13465585674597037252], [8183962687652916671, "spl_program_error", false, 15444992088162225235], [9529943735784919782, "arrayref", false, 16217418545956212235], [11128316282551294881, "spl_pod", false, 11853271019816047248], [15222034437690075872, "spl_type_length_value", false, 8092816564374268059], [17231035582851916918, "solana_program", false, 7143540289734359807]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-transfer-hook-interface-f283cc044414e295/dep-lib-spl_transfer_hook_interface", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}