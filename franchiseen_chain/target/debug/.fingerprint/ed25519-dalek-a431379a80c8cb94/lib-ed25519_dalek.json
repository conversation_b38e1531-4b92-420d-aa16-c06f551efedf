{"rustc": 17575471286409424799, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 16409354033026609460, "profile": 8276155916380437441, "path": 11531104833902528410, "deps": [[2932480923465029663, "zeroize", false, 8062885236115174975], [4731167174326621189, "rand", false, 8674787314126584615], [9431183304631869056, "curve25519_dalek", false, 12400519084799559175], [9689903380558560274, "serde_crate", false, 14160843880739812629], [11472355562936271783, "sha2", false, 1875802334563756702], [16629266738323756185, "ed25519", false, 7063812621389361283]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ed25519-dalek-a431379a80c8cb94/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}