{"rustc": 17575471286409424799, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 5408242616063297496, "profile": 11536551860721242960, "path": 5039062193296425690, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-ff2a91fe25c4a666/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}