{"rustc": 17575471286409424799, "features": "[\"default\", \"hmac\", \"hmac-drbg\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "declared_features": "[\"default\", \"hmac\", \"hmac-drbg\", \"lazy-static-context\", \"lazy_static\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "target": 3229137391415082075, "profile": 8276155916380437441, "path": 16894621337606910823, "deps": [[326483822194815791, "hmac_drbg", false, 8065855672208470794], [4731167174326621189, "rand", false, 8674787314126584615], [6374421995994392543, "digest", false, 7923676152937876126], [9529943735784919782, "arrayref", false, 16217418545956212235], [9689903380558560274, "serde", false, 14160843880739812629], [10697153736615144157, "build_script_build", false, 1731493603540353084], [11472355562936271783, "sha2", false, 1875802334563756702], [13443824959912985638, "libsecp256k1_core", false, 2176310283155371251], [17001665395952474378, "typenum", false, 507280138290573205], [17072468807347166763, "base64", false, 15942712747471338807]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsecp256k1-bab85c747d566bb1/dep-lib-libsecp256k1", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}