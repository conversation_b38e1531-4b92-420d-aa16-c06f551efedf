{"rustc": 17575471286409424799, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 1331003669356655947, "path": 1862987059685335171, "deps": [[2828590642173593838, "cfg_if", false, 18120079818586240257], [3722963349756955755, "once_cell", false, 8856211945369855167], [6946689283190175495, "build_script_build", false, 6368719578803850977], [11382113702854245495, "wasm_bindgen_macro", false, 2270298342905581310], [14156967978702956262, "rustversion", false, 6004720243463137720]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-91d7e086cb315647/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}