{"rustc": 17575471286409424799, "features": "[\"derive\"]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"anchor-lang-idl\", \"derive\", \"event-cpi\", \"idl-build\", \"init-if-needed\", \"interface-instructions\"]", "target": 8056595410447424620, "profile": 8276155916380437441, "path": 3642465151255129132, "deps": [[65234016722529558, "bincode", false, 8378392133955702329], [2611905835808443941, "borsh", false, 16219690414155524163], [3999094449721941670, "anchor_attribute_error", false, 6293693676300577454], [5526981839279233247, "anchor_attribute_constant", false, 7757201316748585157], [6643739152182419278, "bytemuck", false, 13465585674597037252], [8008191657135824715, "thiserror", false, 4538958720104521460], [8429694477255964756, "anchor_attribute_event", false, 10851275453756369751], [9208777977497763370, "anchor_attribute_account", false, 10106895963466029302], [9529943735784919782, "arrayref", false, 16217418545956212235], [9920160576179037441, "getrandom", false, 1881186672664555700], [10436595253812245980, "anchor_attribute_access_control", false, 4286551084397276902], [14561437374539644362, "solana_program", false, 6942158094092361283], [15792991961774012505, "anchor_attribute_program", false, 7395415997473612832], [17500248564313084819, "anchor_derive_accounts", false, 1794219993104148069], [17598592703151118011, "anchor_derive_serde", false, 13486012288110667285], [18066890886671768183, "base64", false, 8304252830160499291], [18409235022284521826, "anchor_derive_space", false, 15442476841322947132]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-ae1bdc0599afded9/dep-lib-anchor_lang", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}